﻿namespace WinFormsApp1
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            cbox1 = new CheckBox();
            cbox2 = new CheckBox();
            cbox4 = new CheckBox();
            cbox3 = new CheckBox();
            label1 = new Label();
            label2 = new Label();
            label3 = new Label();
            label4 = new Label();
            nDownMin = new NumericUpDown();
            nDownMax = new NumericUpDown();
            label5 = new Label();
            label6 = new Label();
            nDownTiPageSet = new NumericUpDown();
            nDownPageCount = new NumericUpDown();
            btnTiGen = new Button();
            btnExport = new Button();
            dataGridView1 = new DataGridView();
            ((System.ComponentModel.ISupportInitialize)nDownMin).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nDownMax).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nDownTiPageSet).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nDownPageCount).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            SuspendLayout();
            // 
            // cbox1
            // 
            cbox1.AutoSize = true;
            cbox1.Location = new Point(22, 39);
            cbox1.Name = "cbox1";
            cbox1.Size = new Size(51, 21);
            cbox1.TabIndex = 0;
            cbox1.Text = "加法";
            cbox1.UseVisualStyleBackColor = true;
            // 
            // cbox2
            // 
            cbox2.AutoSize = true;
            cbox2.Location = new Point(83, 39);
            cbox2.Name = "cbox2";
            cbox2.Size = new Size(51, 21);
            cbox2.TabIndex = 1;
            cbox2.Text = "减法";
            cbox2.UseVisualStyleBackColor = true;
            // 
            // cbox4
            // 
            cbox4.AutoSize = true;
            cbox4.Location = new Point(83, 66);
            cbox4.Name = "cbox4";
            cbox4.Size = new Size(51, 21);
            cbox4.TabIndex = 3;
            cbox4.Text = "除法";
            cbox4.UseVisualStyleBackColor = true;
            // 
            // cbox3
            // 
            cbox3.AutoSize = true;
            cbox3.Location = new Point(22, 66);
            cbox3.Name = "cbox3";
            cbox3.Size = new Size(51, 21);
            cbox3.TabIndex = 2;
            cbox3.Text = "乘法";
            cbox3.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(28, 11);
            label1.Name = "label1";
            label1.Size = new Size(56, 17);
            label1.TabIndex = 4;
            label1.Text = "运算类型";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(26, 106);
            label2.Name = "label2";
            label2.Size = new Size(56, 17);
            label2.TabIndex = 5;
            label2.Text = "数值范围";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(26, 142);
            label3.Name = "label3";
            label3.Size = new Size(44, 17);
            label3.TabIndex = 6;
            label3.Text = "最小值";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(26, 181);
            label4.Name = "label4";
            label4.Size = new Size(44, 17);
            label4.TabIndex = 7;
            label4.Text = "最大值";
            // 
            // nDownMin
            // 
            nDownMin.Location = new Point(76, 142);
            nDownMin.Name = "nDownMin";
            nDownMin.Size = new Size(71, 23);
            nDownMin.TabIndex = 8;
            // 
            // nDownMax
            // 
            nDownMax.Location = new Point(76, 179);
            nDownMax.Name = "nDownMax";
            nDownMax.Size = new Size(71, 23);
            nDownMax.TabIndex = 9;
            nDownMax.Value = new decimal(new int[] { 100, 0, 0, 0 });
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(29, 227);
            label5.Name = "label5";
            label5.Size = new Size(88, 17);
            label5.TabIndex = 10;
            label5.Text = "题目设置(每页)";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(29, 288);
            label6.Name = "label6";
            label6.Size = new Size(44, 17);
            label6.TabIndex = 11;
            label6.Text = "总页数";
            // 
            // nDownTiPageSet
            // 
            nDownTiPageSet.Location = new Point(29, 247);
            nDownTiPageSet.Name = "nDownTiPageSet";
            nDownTiPageSet.Size = new Size(121, 23);
            nDownTiPageSet.TabIndex = 12;
            nDownTiPageSet.Value = new decimal(new int[] { 100, 0, 0, 0 });
            // 
            // nDownPageCount
            // 
            nDownPageCount.Location = new Point(28, 308);
            nDownPageCount.Name = "nDownPageCount";
            nDownPageCount.Size = new Size(122, 23);
            nDownPageCount.TabIndex = 13;
            nDownPageCount.Value = new decimal(new int[] { 1, 0, 0, 0 });
            // 
            // btnTiGen
            // 
            btnTiGen.Location = new Point(29, 354);
            btnTiGen.Name = "btnTiGen";
            btnTiGen.Size = new Size(121, 23);
            btnTiGen.TabIndex = 14;
            btnTiGen.Text = "生成题目";
            btnTiGen.UseVisualStyleBackColor = true;
            btnTiGen.Click += btnTiGen_Click;
            // 
            // btnExport
            // 
            btnExport.Location = new Point(29, 397);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(121, 23);
            btnExport.TabIndex = 15;
            btnExport.Text = "导出PDF";
            btnExport.UseVisualStyleBackColor = true;
            btnExport.Click += btnExport_Click;
            // 
            // dataGridView1
            // 
            dataGridView1.AllowUserToAddRows = false;
            dataGridView1.AllowUserToDeleteRows = false;
            dataGridView1.AllowUserToResizeColumns = false;
            dataGridView1.AllowUserToResizeRows = false;
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView1.ColumnHeadersVisible = false;
            dataGridView1.Location = new Point(200, 39);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.ReadOnly = true;
            dataGridView1.RowHeadersVisible = false;
            dataGridView1.Size = new Size(570, 399);
            dataGridView1.TabIndex = 16;
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(774, 441);
            Controls.Add(btnExport);
            Controls.Add(btnTiGen);
            Controls.Add(nDownPageCount);
            Controls.Add(nDownTiPageSet);
            Controls.Add(label6);
            Controls.Add(label5);
            Controls.Add(nDownMax);
            Controls.Add(nDownMin);
            Controls.Add(label4);
            Controls.Add(label3);
            Controls.Add(label2);
            Controls.Add(label1);
            Controls.Add(cbox3);
            Controls.Add(cbox4);
            Controls.Add(cbox2);
            Controls.Add(cbox1);
            Controls.Add(dataGridView1);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            MaximizeBox = false;
            Name = "Form1";
            Text = "加减乘除计算题";
            ((System.ComponentModel.ISupportInitialize)nDownMin).EndInit();
            ((System.ComponentModel.ISupportInitialize)nDownMax).EndInit();
            ((System.ComponentModel.ISupportInitialize)nDownTiPageSet).EndInit();
            ((System.ComponentModel.ISupportInitialize)nDownPageCount).EndInit();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private CheckBox cbox1;
        private CheckBox cbox2;
        private CheckBox cbox4;
        private CheckBox cbox3;
        private Label label1;
        private Label label2;
        private Label label3;
        private Label label4;
        private NumericUpDown nDownMin;
        private NumericUpDown nDownMax;
        private Label label5;
        private Label label6;
        private NumericUpDown nDownTiPageSet;
        private NumericUpDown nDownPageCount;
        private Button btnTiGen;
        private Button btnExport;
        private DataGridView dataGridView1;
    }
}
