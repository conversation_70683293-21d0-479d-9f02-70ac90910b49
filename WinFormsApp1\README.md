# 数学题目生成器 使用说明书

## 📋 项目简介

数学题目生成器是一个基于 Windows Forms 的桌面应用程序，用于自动生成加减乘除数学计算题，并支持导出为 PDF 格式。适用于教师、家长为学生准备练习题。

## 🚀 功能特性

- ✅ 支持四种运算类型：加法、减法、乘法、除法
- ✅ 自定义数值范围（最小值、最大值）
- ✅ 可设置每页题目数量和总页数
- ✅ 实时预览生成的题目
- ✅ 导出为 PDF 格式，支持中文字体
- ✅ 智能算法确保减法结果为正数，除法结果为整数

## 🛠️ 系统要求

- **操作系统**: Windows 10/11
- **.NET 版本**: .NET 8.0 或更高版本
- **依赖库**: iTextSharp 5.5.13.3

## 📦 安装说明

### 方式一：直接运行（推荐）
1. 下载项目文件
2. 确保已安装 .NET 8.0 Runtime
3. 双击 `WinFormsApp1.exe` 运行

### 方式二：从源码编译
```bash
# 克隆项目
git clone [项目地址]

# 进入项目目录
cd WinFormsApp1

# 还原依赖包
dotnet restore

# 编译项目
dotnet build

# 运行程序
dotnet run
```

## 🎯 使用指南

### 1. 界面布局

程序界面分为两个主要区域：
- **左侧控制面板**: 参数设置区域
- **右侧预览区域**: 题目显示区域

### 2. 参数设置

#### 运算类型选择
- **加法** (✓): 生成加法题目，如 `15 + 23 = `
- **减法** (✓): 生成减法题目，确保结果为正数，如 `45 - 12 = `
- **乘法** (✓): 生成乘法题目，如 `7 × 8 = `
- **除法** (✓): 生成除法题目，确保能整除，如 `56 ÷ 7 = `

> 💡 **提示**: 可以同时选择多种运算类型，程序会随机混合生成

#### 数值范围设置
- **最小值**: 设置参与运算的数字最小值（默认: 0）
- **最大值**: 设置参与运算的数字最大值（默认: 100）

> ⚠️ **注意**: 最小值不能大于最大值，且不能为负数

#### 题目数量设置
- **题目设置(每页)**: 每页显示的题目数量（默认: 100）
- **总页数**: 生成的总页数（默认: 1）

### 3. 生成题目

1. 根据需要勾选运算类型
2. 设置合适的数值范围
3. 调整题目数量和页数
4. 点击 **"生成题目"** 按钮
5. 在右侧预览区域查看生成的题目

### 4. 导出PDF

1. 确保已生成题目
2. 点击 **"导出PDF"** 按钮
3. 在弹出的保存对话框中选择保存位置
4. 输入文件名（默认: "数学题目.pdf"）
5. 点击保存，等待导出完成

## 🔧 高级功能

### 智能算法特性

#### 减法优化
- 自动确保被减数大于减数
- 避免结果为 0 的情况
- 保证所有减法结果为正整数

#### 除法优化
- 自动生成能整除的除法题目
- 避免除数为 0 或 1 的情况
- 确保商在指定范围内

#### 防死循环机制
- 设置最大尝试次数（1000次）
- 当无法生成满足条件的题目时自动提示
- 建议用户调整参数设置

### PDF 导出特性

#### 字体支持
- 优先使用系统中文字体（黑体）
- 字体加载失败时自动降级为英文字体
- 确保 PDF 文件的兼容性

#### 布局优化
- A4 纸张大小，适合打印
- 每行显示 4 道题目
- 合理的行间距和页边距
- 自动分页处理

## ❗ 常见问题

### Q1: 程序提示"请至少选择一种运算类型"
**A**: 请确保至少勾选一个运算类型复选框（加法、减法、乘法、除法）

### Q2: 程序提示"最小值不能大于最大值"
**A**: 请检查数值范围设置，确保最小值 ≤ 最大值

### Q3: 程序提示"无法生成满足条件的题目"
**A**: 参数设置过于严格，建议：
- 扩大数值范围
- 减少每页题目数量
- 调整运算类型组合

### Q4: PDF 导出后中文显示异常
**A**: 程序会自动处理字体问题，如仍有问题请：
- 确保系统安装了中文字体
- 检查 PDF 阅读器是否支持中文

### Q5: 除法题目太简单或太难
**A**: 调整数值范围：
- 简单题目：设置较小的数值范围（如 1-20）
- 困难题目：设置较大的数值范围（如 10-100）

## 🐛 错误处理

程序内置了完善的错误处理机制：

1. **参数验证**: 自动检查用户输入的合理性
2. **异常捕获**: 捕获并友好提示各种运行时错误
3. **资源管理**: 自动释放文件流和PDF文档资源
4. **容错机制**: 字体加载失败时自动使用备用方案

## 📝 更新日志

### v1.1 (最新版本)
- ✅ 修复除法逻辑错误，避免无限循环
- ✅ 添加数值范围验证
- ✅ 改进中文字体支持
- ✅ 优化错误处理机制
- ✅ 清理冗余代码，提高性能

### v1.0 (初始版本)
- ✅ 基础四则运算题目生成
- ✅ PDF 导出功能
- ✅ 简单的用户界面

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 首先查阅本说明书的"常见问题"部分
2. 检查系统环境是否满足要求
3. 尝试重启程序或重新安装

## 📄 许可证

本项目采用 MIT 许可证，允许自由使用、修改和分发。

---

**祝您使用愉快！** 🎉
