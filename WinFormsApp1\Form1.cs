using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;
using Document = iTextSharp.text.Document;
using Font = iTextSharp.text.Font;

namespace WinFormsApp1
{
    public partial class Form1 : Form
    {
        // 常量定义
        private const int MAX_GENERATION_ATTEMPTS = 1000;
        private const float PDF_ROW_HEIGHT = 25f;
        private const float PDF_SPACING = 10f;
        private const int PDF_FONT_SIZE = 10;

        public Form1()
        {
            InitializeComponent();
        }



        private void btnTiGen_Click(object sender, EventArgs e)
        {
            List<string> operators = new List<string>();
            if (cbox1.Checked) operators.Add("+");
            if (cbox2.Checked) operators.Add("-");
            if (cbox3.Checked) operators.Add("×");
            if (cbox4.Checked) operators.Add("÷");

            if (operators.Count == 0)
            {
                MessageBox.Show("请至少选择一种运算类型！");
                return;
            }

            Random random = new Random();
            int pageCount = (int)nDownPageCount.Value;
            int problemsPerPage = (int)nDownTiPageSet.Value;
            int minValue = (int)nDownMin.Value;
            int maxValue = (int)nDownMax.Value;

            // 验证数值范围
            if (minValue > maxValue)
            {
                MessageBox.Show("最小值不能大于最大值！");
                return;
            }

            if (minValue < 0)
            {
                MessageBox.Show("最小值不能为负数！");
                return;
            }

            // 清空并设置列
            dataGridView1.Rows.Clear();
            dataGridView1.Columns.Clear();
            for (int i = 0; i < 4; i++)
            {
                var column = new DataGridViewColumn();
                column.CellTemplate = new DataGridViewTextBoxCell();
                column.Width = 120;
                dataGridView1.Columns.Add(column);
            }

            for (int page = 1; page <= pageCount; page++)
            {
                // 添加页标题行
                int rowIndex = dataGridView1.Rows.Add();
                var titleCell = dataGridView1.Rows[rowIndex].Cells[0];
                titleCell.Value = $"Page{page}";
                titleCell.Style.Font = new System.Drawing.Font(dataGridView1.Font.FontFamily, dataGridView1.Font.Size, FontStyle.Bold);

                // 生成题目
                for (int i = 0; i < problemsPerPage; i += 4)
                {
                    rowIndex = dataGridView1.Rows.Add();
                    for (int j = 0; j < 4 && (i + j) < problemsPerPage; j++)
                    {
                        string? problem = GenerateProblem(random, minValue, maxValue, operators);
                        if (problem == null)
                        {
                            MessageBox.Show("无法生成满足条件的题目，请调整参数设置！");
                            return;
                        }
                        dataGridView1.Rows[rowIndex].Cells[j].Value = $"{problem} = ";
                        dataGridView1.Rows[rowIndex].Height = (int)PDF_ROW_HEIGHT;
                    }
                }

                if (page < pageCount)
                {
                    dataGridView1.Rows.Add(); // 页间空行
                }
            }

            // 设置表格样式
            dataGridView1.GridColor = Color.LightGray;
            dataGridView1.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            dataGridView1.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridView1.DefaultCellStyle.Font = new System.Drawing.Font("Microsoft YaHei", 9F, FontStyle.Regular);
            dataGridView1.DefaultCellStyle.SelectionBackColor = dataGridView1.DefaultCellStyle.BackColor;
            dataGridView1.DefaultCellStyle.SelectionForeColor = dataGridView1.DefaultCellStyle.ForeColor;

            // 自动调整最后一列宽度
            dataGridView1.Columns[dataGridView1.Columns.Count - 1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;

        }

        private string? GenerateProblem(Random random, int minValue, int maxValue, List<string> operators)
        {
            int attempts = 0;

            while (attempts < MAX_GENERATION_ATTEMPTS)
            {
                attempts++;

                int num1 = random.Next(minValue, maxValue + 1);
                int num2 = random.Next(minValue, maxValue + 1);
                string op = operators[random.Next(operators.Count)];

                // 处理减法：确保结果为正数且不为0
                if (op == "-")
                {
                    if (num1 <= num2)
                    {
                        // 交换数字确保结果为正
                        int temp = num1;
                        num1 = num2;
                        num2 = temp;
                    }
                    // 如果结果为0，跳过这次生成
                    if (num1 == num2) continue;
                }

                // 处理除法：确保能整除且除数不为0
                if (op == "÷")
                {
                    if (num2 == 0 || num2 == 1) continue; // 避免除以0或1

                    // 重新生成被除数，确保能整除
                    int quotient = random.Next(minValue, Math.Min(maxValue / num2, maxValue) + 1);
                    if (quotient == 0) continue; // 避免商为0

                    num1 = quotient * num2;

                    // 确保被除数在范围内
                    if (num1 > maxValue) continue;
                }

                return $"{num1} {op} {num2}";
            }

            // 如果尝试次数过多仍无法生成，返回null
            return null;
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            if (dataGridView1.Rows.Count == 0)
            {
                MessageBox.Show("请先生成题目！");
                return;
            }

            SaveFileDialog saveDialog = new SaveFileDialog
            {
                Filter = "PDF文件|*.pdf",
                FileName = "数学题目.pdf",
                Title = "导出PDF"
            };

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 创建PDF文档
                    using (FileStream fs = new FileStream(saveDialog.FileName, FileMode.Create))
                    using (Document document = new Document(PageSize.A4, 40, 40, 40, 40))
                    {
                        PdfWriter writer = PdfWriter.GetInstance(document, fs);
                        document.Open();

                        // 尝试使用支持中文的字体，如果失败则使用默认字体
                        BaseFont baseFont;
                        try
                        {
                            // 尝试使用系统中文字体
                            string fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts), "simhei.ttf");
                            if (File.Exists(fontPath))
                            {
                                baseFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                            }
                            else
                            {
                                // 如果没有中文字体，使用默认字体
                                baseFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
                            }
                        }
                        catch
                        {
                            // 字体加载失败时使用默认字体
                            baseFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
                        }

                        iTextSharp.text.Font font = new iTextSharp.text.Font(baseFont, PDF_FONT_SIZE);
                        iTextSharp.text.Font boldFont = new iTextSharp.text.Font(baseFont, PDF_FONT_SIZE, iTextSharp.text.Font.BOLD);

                        // 遍历DataGridView的行
                        int currentPage = 0;
                        PdfPTable? table = null;

                        for (int i = 0; i < dataGridView1.Rows.Count; i++)
                        {
                            var row = dataGridView1.Rows[i];
                            string firstCellValue = row.Cells[0].Value?.ToString() ?? "";

                            // 检查是否是页标题行
                            if (firstCellValue.StartsWith("Page"))
                            {
                                currentPage++;
                                if (table != null)
                                {
                                    document.Add(table);
                                    document.NewPage();
                                }

                                // 添加页标题
                                Paragraph title = new Paragraph(firstCellValue, boldFont)
                                {
                                    Alignment = Element.ALIGN_LEFT,
                                    SpacingAfter = 20f
                                };
                                document.Add(title);

                                // 创建新表格
                                table = new PdfPTable(4)
                                {
                                    WidthPercentage = 100,
                                    SpacingBefore = PDF_SPACING,
                                    SpacingAfter = PDF_SPACING
                                };
                                table.SetWidths(new float[] { 1f, 1f, 1f, 1f });
                            }
                            else if (table != null)
                            {
                                // 添加题目
                                for (int j = 0; j < 4; j++)
                                {
                                    string cellValue = row.Cells[j].Value?.ToString() ?? "";
                                    PdfPCell cell = new PdfPCell(new Phrase(cellValue, font))
                                    {
                                        MinimumHeight = PDF_ROW_HEIGHT,
                                        VerticalAlignment = Element.ALIGN_MIDDLE,
                                        HorizontalAlignment = Element.ALIGN_LEFT,
                                        PaddingLeft = 5f
                                    };
                                    table.AddCell(cell);
                                }
                            }
                        }

                        // 添加最后一个表格
                        if (table != null)
                        {
                            document.Add(table);
                        }

                        document.Close();
                    }

                    MessageBox.Show("PDF导出成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出PDF时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
