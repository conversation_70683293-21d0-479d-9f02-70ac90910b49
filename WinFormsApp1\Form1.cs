using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Drawing.Printing;
using System.IO;
using System.Reflection.Metadata;
using System.Xml.Linq;
using Document = iTextSharp.text.Document;
using Font = iTextSharp.text.Font;

namespace WinFormsApp1
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }



        private void btnTiGen_Click(object sender, EventArgs e)
        {
            List<string> operators = new List<string>();
            if (cbox1.Checked) operators.Add("+");
            if (cbox2.Checked) operators.Add("-");
            if (cbox3.Checked) operators.Add("×");
            if (cbox4.Checked) operators.Add("÷");

            if (operators.Count == 0)
            {
                MessageBox.Show("请至少选择一种运算类型！");
                return;
            }

            Random random = new Random();
            int pageCount = (int)nDownPageCount.Value;
            int problemsPerPage = (int)nDownTiPageSet.Value;
            int minValue = (int)nDownMin.Value;
            int maxValue = (int)nDownMax.Value;

            // 清空并设置列
            dataGridView1.Rows.Clear();
            dataGridView1.Columns.Clear();
            for (int i = 0; i < 4; i++)
            {
                var column = new DataGridViewColumn();
                column.CellTemplate = new DataGridViewTextBoxCell();
                column.Width = 120;
                dataGridView1.Columns.Add(column);
            }

            for (int page = 1; page <= pageCount; page++)
            {
                // 添加页标题行
                int rowIndex = dataGridView1.Rows.Add();
                var titleCell = dataGridView1.Rows[rowIndex].Cells[0];
                titleCell.Value = $"Page{page}";
                titleCell.Style.Font = new System.Drawing.Font(dataGridView1.Font.FontFamily, dataGridView1.Font.Size, FontStyle.Bold);

                // 生成题目
                for (int i = 0; i < problemsPerPage; i += 4)
                {
                    rowIndex = dataGridView1.Rows.Add();
                    for (int j = 0; j < 4 && (i + j) < problemsPerPage; j++)
                    {
                        string problem = GenerateProblem(random, minValue, maxValue, operators);
                        dataGridView1.Rows[rowIndex].Cells[j].Value = $"{problem} = ";
                        dataGridView1.Rows[rowIndex].Height = 25;
                    }
                }

                if (page < pageCount)
                {
                    dataGridView1.Rows.Add(); // 页间空行
                }
            }

            // 设置表格样式
            dataGridView1.GridColor = Color.LightGray;
            dataGridView1.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            dataGridView1.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridView1.DefaultCellStyle.Font = new System.Drawing.Font("Microsoft YaHei", 9F, FontStyle.Regular);
            dataGridView1.DefaultCellStyle.SelectionBackColor = dataGridView1.DefaultCellStyle.BackColor;
            dataGridView1.DefaultCellStyle.SelectionForeColor = dataGridView1.DefaultCellStyle.ForeColor;

            // 自动调整最后一列宽度
            dataGridView1.Columns[dataGridView1.Columns.Count - 1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;

        }

        private string GenerateProblem(Random random, int minValue, int maxValue, List<string> operators)
        {
            int num1, num2;
            string op;

            do
            {
                num1 = random.Next(minValue, maxValue + 1);
                num2 = random.Next(minValue, maxValue + 1);
                op = operators[random.Next(operators.Count)];

                // 确保减法结果为正数
                if (op == "-" && num1 < num2)
                {
                    int temp = num1;
                    num1 = num2;
                    num2 = temp;
                }

                // 处理除法
                if (op == "÷")
                {
                    // 确保能整除
                    if (num2 == 0) continue;
                    int product = num1 * num2;
                    if (product <= maxValue)
                    {
                        num1 = product;
                    }
                    else
                    {
                        continue;
                    }
                }
            } while ((op == "-" && num1 == num2) || // 避免减法结果为0
                     (op == "÷" && (num2 == 0 || num1 % num2 != 0))); // 确保除法能整除

            return $"{num1} {op} {num2}";
        }

        private void btnExport_Click(object sender, EventArgs e)
        {

            //try
            //{
            //    using (var fs = File.Create("D:\\test_itext.pdf"))
            //    using (var doc = new Document())
            //    {
            //        // 确保不使用中文字体，仅测试基础功能
            //        PdfWriter writer = PdfWriter.GetInstance(doc, fs);
            //        doc.Open();
            //        doc.Add(new Paragraph("Hello PDF!"));
            //    }
            //    MessageBox.Show("✅ PDF 生成成功！");
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"❌ 崩溃点：[{ex.TargetSite?.Name}]\n错误类型：{ex.GetType().Name}\n详情：{ex.Message}");
            //}
        



            if (dataGridView1.Rows.Count == 0)
            {
                MessageBox.Show("请先生成题目！");
                return;
            }

            SaveFileDialog saveDialog = new SaveFileDialog
            {
                Filter = "PDF文件|*.pdf",
                FileName = "数学题目.pdf",
                Title = "导出PDF"
            };

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 创建PDF文档
                    using (FileStream fs = new FileStream(saveDialog.FileName, FileMode.Create))
                    {
                        if (fs == null)
                        {
                            MessageBox.Show("文件流创建失败！");
                            return;
                        }
                        Document document = new Document(PageSize.A4, 40, 40, 40, 40);
                        PdfWriter writer = PdfWriter.GetInstance(document, fs);
                        document.Open();


                        BaseFont baseFont = BaseFont.CreateFont(
                        BaseFont.HELVETICA,  // 西文字体
                        BaseFont.WINANSI,    // 西文编码
                        BaseFont.NOT_EMBEDDED
                    );


                        // 设置中文字体
                        // BaseFont baseFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                        iTextSharp.text.Font font = new iTextSharp.text.Font(baseFont, 10);
                        iTextSharp.text.Font boldFont = new iTextSharp.text.Font(baseFont, 10, iTextSharp.text.Font.BOLD);

                        // 遍历DataGridView的行
                        int currentPage = 0;
                        PdfPTable table = null;

                        for (int i = 0; i < dataGridView1.Rows.Count; i++)
                        {
                            var row = dataGridView1.Rows[i];
                            string firstCellValue = row.Cells[0].Value?.ToString() ?? "";

                            // 检查是否是页标题行
                            if (firstCellValue.StartsWith("Page"))
                            {
                                currentPage++;
                                if (table != null)
                                {
                                    document.Add(table);
                                    document.NewPage();
                                }

                                // 添加页标题
                                Paragraph title = new Paragraph(firstCellValue, boldFont)
                                {
                                    Alignment = Element.ALIGN_LEFT,
                                    SpacingAfter = 20f
                                };
                                document.Add(title);

                                // 创建新表格
                                table = new PdfPTable(4)
                                {
                                    WidthPercentage = 100,
                                    SpacingBefore = 10f,
                                    SpacingAfter = 10f
                                };
                                table.SetWidths(new float[] { 1f, 1f, 1f, 1f });
                            }
                            else if (table != null)
                            {
                                // 添加题目
                                for (int j = 0; j < 4; j++)
                                {
                                    string cellValue = row.Cells[j].Value?.ToString() ?? "";
                                    PdfPCell cell = new PdfPCell(new Phrase(cellValue, font))
                                    {
                                        MinimumHeight = 25f,
                                        VerticalAlignment = Element.ALIGN_MIDDLE,
                                        HorizontalAlignment = Element.ALIGN_LEFT,
                                        PaddingLeft = 5f
                                    };
                                    table.AddCell(cell);
                                }
                            }
                        }

                        // 添加最后一个表格
                        if (table != null)
                        {
                            document.Add(table);
                        }

                        document.Close();
                    }

                    MessageBox.Show("PDF导出成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出PDF时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
