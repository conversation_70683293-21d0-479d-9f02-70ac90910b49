# 🚀 数学题目生成器 - 快速入门

## 3分钟上手指南

### 第1步：选择运算类型 ✅
在左侧勾选您需要的运算类型：
- ☑️ 加法 - 生成如 `12 + 8 = ` 的题目
- ☑️ 减法 - 生成如 `25 - 7 = ` 的题目  
- ☑️ 乘法 - 生成如 `6 × 9 = ` 的题目
- ☑️ 除法 - 生成如 `48 ÷ 6 = ` 的题目

> 💡 可以同时选择多种类型！

### 第2步：设置数值范围 🎯
- **最小值**: 比如设置为 `1`
- **最大值**: 比如设置为 `50`

这样生成的题目数字都在 1-50 之间

### 第3步：设置题目数量 📊
- **每页题目数**: 建议设置 `20-40` 道题
- **总页数**: 根据需要设置，比如 `2` 页

### 第4步：生成题目 🎲
点击 **"生成题目"** 按钮，右侧会显示生成的题目预览

### 第5步：导出PDF 📄
点击 **"导出PDF"** 按钮，选择保存位置，即可获得可打印的PDF文件

## 🎯 推荐设置

### 小学低年级（1-2年级）
```
运算类型: ✅加法 ✅减法
数值范围: 1 - 20
每页题目: 20道
```

### 小学中年级（3-4年级）
```
运算类型: ✅加法 ✅减法 ✅乘法
数值范围: 1 - 50  
每页题目: 30道
```

### 小学高年级（5-6年级）
```
运算类型: ✅加法 ✅减法 ✅乘法 ✅除法
数值范围: 1 - 100
每页题目: 40道
```

## ⚠️ 注意事项

1. **必须选择至少一种运算类型**
2. **最小值不能大于最大值**
3. **生成题目后才能导出PDF**
4. **除法题目自动确保能整除**
5. **减法题目自动确保结果为正数**

## 🆘 遇到问题？

- **提示"无法生成题目"** → 扩大数值范围或减少题目数量
- **PDF中文显示异常** → 程序会自动处理，无需担心
- **程序无响应** → 重启程序，检查参数设置

---
**开始使用吧！只需要5个步骤，3分钟就能生成专业的数学练习题！** 🎉
